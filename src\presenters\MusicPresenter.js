/**
 * MusicPresenter - Connects MusicModel with MusicPlayerView
 * Follows MVP pattern - handles music logic and coordinates between model and view
 */
import { MusicModel } from '../models/MusicModel.js'
import { MusicPlayerView } from '../views/MusicPlayerView.js'
import { EventEmitter } from '../utils/EventEmitter.js'

export class MusicPresenter extends EventEmitter {
  constructor() {
    super()
    
    // Initialize model and view
    this.model = new MusicModel()
    this.view = new MusicPlayerView()
    
    // State
    this.isInitialized = false
    
    this.setupModelListeners()
    this.setupViewCallbacks()
  }
  
  /**
   * Initialize the presenter
   */
  init() {
    if (this.isInitialized) return
    
    // Connect audio element to model
    const audioElement = this.view.getAudioElement()
    if (audioElement) {
      this.model.setAudioElement(audioElement)
    }
    
    this.updateUI()
    this.isInitialized = true
  }
  
  /**
   * Setup model event listeners
   */
  setupModelListeners() {
    // Configuration events
    this.model.on('configUpdated', (config) => {
      this.view.updateMusicEnabled(config.enabled)
      this.view.updateVolumeDisplay(config.volume)
    })
    
    // Track events
    this.model.on('tracksLoaded', (data) => {
      this.view.updateTracksList(data.tracks, data.currentTrack?.id)
      this.view.showSuccess(`Loaded ${data.tracks.length} music file${data.tracks.length !== 1 ? 's' : ''}`)
      this.view.clearFileInput()
    })
    
    this.model.on('trackSelected', (data) => {
      this.view.updateCurrentTrack(data.track)
      this.view.showSuccess(`Selected: ${data.track.name}`)
    })
    
    this.model.on('tracksCleared', () => {
      this.view.updateTracksList([], null)
      this.view.showSuccess('Music files cleared')
    })
    
    // Playback events
    this.model.on('playStarted', () => {
      this.view.showPlaybackIndicator(true)
    })
    
    this.model.on('playPaused', () => {
      this.view.showPlaybackIndicator(false)
    })
    
    this.model.on('playStopped', () => {
      this.view.showPlaybackIndicator(false)
    })
    
    this.model.on('trackEnded', () => {
      // Track ended, will loop automatically for focus music
    })
    
    this.model.on('volumeChanged', (data) => {
      this.view.updateVolumeDisplay(data.volume)
    })
    
    // Loading events
    this.model.on('loadingStarted', () => {
      this.view.setLoadingState(true)
    })
    
    this.model.on('canPlay', () => {
      this.view.setLoadingState(false)
    })
    
    // Error events
    this.model.on('error', (data) => {
      this.view.showError(data.error)
      this.view.setLoadingState(false)
    })
  }
  
  /**
   * Setup view callbacks
   */
  setupViewCallbacks() {
    this.view.setCallbacks({
      onMusicEnabledChange: (enabled) => this.handleMusicEnabledChange(enabled),
      onVolumeChange: (volume) => this.handleVolumeChange(volume),
      onTrackSelect: (trackId) => this.handleTrackSelect(trackId),
      onFilesSelected: (files) => this.handleFilesSelected(files)
    })
  }
  
  /**
   * Update UI based on current model state
   */
  updateUI() {
    const config = this.model.getConfig()
    const state = this.model.getState()
    
    // Update controls
    this.view.updateMusicEnabled(config.enabled)
    this.view.updateVolumeDisplay(config.volume)
    
    // Update tracks list
    const tracksList = this.model.getTracksList()
    this.view.updateTracksList(tracksList, config.currentTrack)
    
    // Update current track
    const currentTrack = this.model.getCurrentTrack()
    if (currentTrack) {
      this.view.updateCurrentTrack(currentTrack)
    }
    
    // Update playback indicator
    this.view.showPlaybackIndicator(state.isPlaying)
  }
  
  /**
   * Handle music enabled/disabled change
   */
  handleMusicEnabledChange(enabled) {
    this.model.updateConfig({ enabled })
    
    if (!enabled && this.model.getState().isPlaying) {
      this.stopMusic()
    }
    
    this.emit('musicEnabledChanged', enabled)
  }
  
  /**
   * Handle volume change
   */
  handleVolumeChange(volume) {
    // Convert from 0-100 to 0-1
    const normalizedVolume = volume / 100
    this.model.setVolume(normalizedVolume)
    this.model.updateConfig({ volume })
    
    this.emit('volumeChanged', volume)
  }
  
  /**
   * Handle track selection
   */
  handleTrackSelect(trackId) {
    if (!trackId) return
    
    const success = this.model.selectTrack(trackId)
    if (success) {
      this.model.updateConfig({ currentTrack: trackId })
      this.emit('trackSelected', trackId)
    }
  }
  
  /**
   * Handle file selection
   */
  handleFilesSelected(files) {
    if (!files || files.length === 0) return
    
    this.view.setLoadingState(true)
    
    try {
      const tracks = this.model.loadMusicFiles(files)
      
      if (tracks.length === 0) {
        this.view.showError('No valid MP3 files found')
      }
    } catch (error) {
      this.view.showError('Failed to load music files')
      console.error('Error loading music files:', error)
    } finally {
      this.view.setLoadingState(false)
    }
  }
  
  /**
   * Start music playback
   */
  startMusic() {
    if (!this.model.getConfig().enabled) {
      return false
    }
    
    if (!this.model.canPlay()) {
      this.view.showError('No music track selected or music is disabled')
      return false
    }
    
    const success = this.model.play()
    if (success) {
      this.emit('musicStarted')
    }
    
    return success
  }
  
  /**
   * Stop music playback
   */
  stopMusic() {
    const success = this.model.stop()
    if (success) {
      this.emit('musicStopped')
    }
    
    return success
  }
  
  /**
   * Pause music playback
   */
  pauseMusic() {
    const success = this.model.pause()
    if (success) {
      this.emit('musicPaused')
    }
    
    return success
  }
  
  /**
   * Resume music playback
   */
  resumeMusic() {
    if (!this.model.getConfig().enabled) {
      return false
    }
    
    const success = this.model.resume()
    if (success) {
      this.emit('musicResumed')
    }
    
    return success
  }
  
  /**
   * Update settings from external source
   */
  updateSettings(settings) {
    if (settings.music) {
      // Convert volume from 0-100 to 0-1 for model
      const musicSettings = { ...settings.music }
      if (musicSettings.volume !== undefined) {
        musicSettings.volume = musicSettings.volume / 100
      }
      
      this.model.updateConfig(musicSettings)
      this.updateUI()
    }
  }
  
  /**
   * Get current music state
   */
  getCurrentState() {
    return {
      config: this.model.getConfig(),
      state: this.model.getState(),
      canPlay: this.model.canPlay(),
      currentTrack: this.model.getCurrentTrack()
    }
  }
  
  /**
   * Clear all music tracks
   */
  clearTracks() {
    this.model.clearTracks()
  }
  
  /**
   * Get tracks list for external access
   */
  getTracksList() {
    return this.model.getTracksList()
  }
  
  /**
   * Check if music is currently playing
   */
  isPlaying() {
    return this.model.getState().isPlaying
  }
  
  /**
   * Check if music is enabled
   */
  isEnabled() {
    return this.model.getConfig().enabled
  }
  
  /**
   * Get current volume (0-100)
   */
  getVolume() {
    return this.model.getConfig().volume * 100
  }
  
  /**
   * Set volume (0-100)
   */
  setVolume(volume) {
    this.handleVolumeChange(volume)
  }
  
  /**
   * Toggle music enabled state
   */
  toggleEnabled() {
    const currentState = this.model.getConfig().enabled
    this.handleMusicEnabledChange(!currentState)
  }
  
  /**
   * Load music files programmatically
   */
  loadMusicFiles(files) {
    this.handleFilesSelected(files)
  }
  
  /**
   * Get music settings for saving
   */
  getMusicSettings() {
    const config = this.model.getConfig()
    return {
      enabled: config.enabled,
      volume: Math.round(config.volume * 100), // Convert back to 0-100
      currentTrack: config.currentTrack,
      autoPlay: config.autoPlay
    }
  }
}
