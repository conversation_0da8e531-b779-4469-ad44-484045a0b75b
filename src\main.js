import './style.css'
import { TimerPresenter } from './presenters/TimerPresenter.js'
import { MusicPresenter } from './presenters/MusicPresenter.js'
import { SettingsPresenter } from './presenters/SettingsPresenter.js'
import { ReportPresenter } from './presenters/ReportPresenter.js'

// Initialize the application
class PomodoroApp {
  constructor() {
    this.timerPresenter = new TimerPresenter()
    this.musicPresenter = new MusicPresenter()
    this.settingsPresenter = new SettingsPresenter()
    this.reportPresenter = new ReportPresenter()

    this.init()
  }

  init() {
    console.log('PomodoroApp: Initializing...')

    // Initialize all presenters
    console.log('PomodoroApp: Initializing presenters...')
    this.settingsPresenter.init()
    this.musicPresenter.init()
    this.reportPresenter.init()
    this.timerPresenter.init()

    // Connect presenters for communication
    this.connectPresenters()

    console.log('PomodoroApp: Initialization complete!')
  }

  connectPresenters() {
    // Timer <-> Music connections
    this.timerPresenter.on('sessionStart', (sessionType) => {
      if (sessionType === 'work' && this.musicPresenter.isEnabled()) {
        this.musicPresenter.startMusic()
      }
    })

    this.timerPresenter.on('sessionEnd', (sessionType) => {
      this.musicPresenter.stopMusic()
    })

    this.timerPresenter.on('timerPause', () => {
      if (this.musicPresenter.isPlaying()) {
        this.musicPresenter.pauseMusic()
      }
    })

    this.timerPresenter.on('timerResume', () => {
      if (this.musicPresenter.isEnabled()) {
        this.musicPresenter.resumeMusic()
      }
    })

    // Timer <-> Settings connections
    this.timerPresenter.on('settingsRequested', () => {
      this.settingsPresenter.showSettings()
    })

    this.settingsPresenter.on('settingsChanged', (settings) => {
      // Update timer with new settings
      this.timerPresenter.updateSettings(settings)

      // Update music with new settings
      this.musicPresenter.updateSettings(settings)
    })

    this.settingsPresenter.on('settingsShown', () => {
      // Update music state in settings when shown
      const musicState = this.musicPresenter.getCurrentState()
      this.settingsPresenter.updateMusicState(musicState)
    })

    // Music <-> Settings connections
    this.musicPresenter.on('tracksLoaded', (data) => {
      // Update settings with new tracks
      this.settingsPresenter.updateMusicTracks(data.tracks, data.currentTrack?.id)
    })

    this.musicPresenter.on('trackSelected', (trackId) => {
      // Update settings when track is selected
      const tracks = this.musicPresenter.getTracksList()
      this.settingsPresenter.updateMusicTracks(tracks, trackId)
    })

    this.settingsPresenter.on('musicFilesSelected', (files) => {
      // Forward file selection to music presenter
      this.musicPresenter.loadMusicFiles(files)
    })

    // Report <-> Timer connections
    this.reportPresenter.on('requestStats', () => {
      const stats = this.timerPresenter.getStats()
      this.reportPresenter.updateReport(stats)
    })

    // Global keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Global shortcuts that work anywhere
      if (e.ctrlKey || e.metaKey) {
        switch (e.code) {
          case 'Comma': // Ctrl/Cmd + ,
            e.preventDefault()
            this.settingsPresenter.toggleSettings()
            break
        }
      }
    })

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
      // Timer and music should continue running in background
      // No action needed - let them run continuously
      if (!document.hidden) {
        // Tab is visible - ensure music is playing if timer is running and music is enabled
        if (this.timerPresenter.isRunning() && this.musicPresenter.isEnabled() && !this.musicPresenter.isPlaying()) {
          this.musicPresenter.resumeMusic()
        }
      }
    })
  }

  /**
   * Get current application state
   */
  getState() {
    return {
      timer: this.timerPresenter.getCurrentState(),
      music: this.musicPresenter.getCurrentState(),
      settings: this.settingsPresenter.getSettings()
    }
  }

  /**
   * Export application data
   */
  exportData() {
    return {
      settings: this.settingsPresenter.getSettings(),
      stats: this.timerPresenter.getStats()
    }
  }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Show content after CSS is loaded
  document.body.classList.add('loaded')

  // Initialize app
  window.pomodoroApp = new PomodoroApp()
})
