/**
 * MusicPlayerView - Handles music player UI (integrated into settings)
 * Follows MVP pattern - only handles DOM manipulation and UI events
 */
export class MusicPlayerView {
  constructor() {
    // DOM elements
    this.elements = {
      audioPlayer: null,
      musicEnabled: null,
      musicVolume: null,
      volumeDisplay: null,
      musicTrack: null,
      loadMusicBtn: null,
      musicFileInput: null
    }
    
    // Event callbacks (set by presenter)
    this.callbacks = {}
    
    this.initializeElements()
    this.setupEventListeners()
  }
  
  /**
   * Initialize DOM elements
   */
  initializeElements() {
    this.elements.audioPlayer = document.getElementById('music-player')
    this.elements.musicEnabled = document.getElementById('music-enabled')
    this.elements.musicVolume = document.getElementById('music-volume')
    this.elements.volumeDisplay = document.getElementById('volume-display')
    this.elements.musicTrack = document.getElementById('music-track')
    this.elements.loadMusicBtn = document.getElementById('load-music-btn')
    this.elements.musicFileInput = document.getElementById('music-file-input')
  }
  
  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Music enabled checkbox
    if (this.elements.musicEnabled) {
      this.elements.musicEnabled.addEventListener('change', (e) => {
        this.callbacks.onMusicEnabledChange?.(e.target.checked)
      })
    }
    
    // Volume slider
    if (this.elements.musicVolume) {
      this.elements.musicVolume.addEventListener('input', (e) => {
        const volume = parseInt(e.target.value)
        this.updateVolumeDisplay(volume)
        this.callbacks.onVolumeChange?.(volume)
      })
    }
    
    // Track selection
    if (this.elements.musicTrack) {
      this.elements.musicTrack.addEventListener('change', (e) => {
        this.callbacks.onTrackSelect?.(e.target.value)
      })
    }
    
    // Load music button
    if (this.elements.loadMusicBtn) {
      this.elements.loadMusicBtn.addEventListener('click', () => {
        this.openFileDialog()
      })
    }
    
    // File input
    if (this.elements.musicFileInput) {
      this.elements.musicFileInput.addEventListener('change', (e) => {
        if (e.target.files && e.target.files.length > 0) {
          this.callbacks.onFilesSelected?.(e.target.files)
        }
      })
    }
  }
  
  /**
   * Set event callbacks
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }
  
  /**
   * Get audio element for model
   */
  getAudioElement() {
    return this.elements.audioPlayer
  }
  
  /**
   * Update music enabled state
   */
  updateMusicEnabled(enabled) {
    if (this.elements.musicEnabled) {
      this.elements.musicEnabled.checked = enabled
    }
    
    // Enable/disable other music controls
    this.setMusicControlsEnabled(enabled)
  }
  
  /**
   * Update volume display
   */
  updateVolumeDisplay(volume) {
    if (this.elements.volumeDisplay) {
      this.elements.volumeDisplay.textContent = `${volume}%`
    }
    
    if (this.elements.musicVolume) {
      this.elements.musicVolume.value = volume
    }
  }
  
  /**
   * Update tracks list
   */
  updateTracksList(tracks, currentTrackId) {
    if (!this.elements.musicTrack) return
    
    // Clear existing options
    this.elements.musicTrack.innerHTML = ''
    
    if (tracks.length === 0) {
      // No tracks available
      const option = document.createElement('option')
      option.value = ''
      option.textContent = 'No music files loaded'
      option.disabled = true
      this.elements.musicTrack.appendChild(option)
      this.elements.musicTrack.disabled = true
    } else {
      // Add default option
      const defaultOption = document.createElement('option')
      defaultOption.value = ''
      defaultOption.textContent = 'Select a track...'
      this.elements.musicTrack.appendChild(defaultOption)
      
      // Add track options
      tracks.forEach(track => {
        const option = document.createElement('option')
        option.value = track.id
        option.textContent = track.name
        
        if (track.duration) {
          option.textContent += ` (${this.formatDuration(track.duration)})`
        }
        
        this.elements.musicTrack.appendChild(option)
      })
      
      // Set current selection
      if (currentTrackId) {
        this.elements.musicTrack.value = currentTrackId
      }
      
      this.elements.musicTrack.disabled = false
    }
  }
  
  /**
   * Set music controls enabled state
   */
  setMusicControlsEnabled(enabled) {
    const controls = [
      this.elements.musicVolume,
      this.elements.musicTrack,
      this.elements.loadMusicBtn
    ]
    
    controls.forEach(control => {
      if (control) {
        control.disabled = !enabled
      }
    })
    
    // Update visual state
    const musicSection = this.elements.musicEnabled?.closest('.settings-section')
    if (musicSection) {
      if (enabled) {
        musicSection.classList.remove('disabled')
      } else {
        musicSection.classList.add('disabled')
      }
    }
  }
  
  /**
   * Open file dialog for music selection
   */
  openFileDialog() {
    if (this.elements.musicFileInput) {
      this.elements.musicFileInput.click()
    }
  }
  
  /**
   * Show loading state for music loading
   */
  setLoadingState(loading) {
    if (!this.elements.loadMusicBtn) return
    
    if (loading) {
      this.elements.loadMusicBtn.disabled = true
      this.elements.loadMusicBtn.textContent = 'Loading...'
      this.elements.loadMusicBtn.classList.add('loading')
    } else {
      this.elements.loadMusicBtn.disabled = false
      this.elements.loadMusicBtn.textContent = 'Load Music Folder'
      this.elements.loadMusicBtn.classList.remove('loading')
    }
  }
  
  /**
   * Show music player status
   */
  showStatus(message, type = 'info') {
    // Create status element
    const status = document.createElement('div')
    status.className = `music-status ${type}`
    status.textContent = message
    
    // Find music section
    const musicSection = this.elements.musicEnabled?.closest('.settings-section')
    if (musicSection) {
      // Remove existing status
      const existingStatus = musicSection.querySelector('.music-status')
      if (existingStatus) {
        existingStatus.remove()
      }
      
      // Add new status
      musicSection.appendChild(status)
      
      // Auto-remove after delay
      setTimeout(() => {
        if (status.parentNode) {
          status.remove()
        }
      }, 3000)
    }
  }
  
  /**
   * Update current track display
   */
  updateCurrentTrack(track) {
    if (!this.elements.musicTrack) return
    
    if (track) {
      this.elements.musicTrack.value = track.id
      
      // Update track option with additional info if needed
      const option = this.elements.musicTrack.querySelector(`option[value="${track.id}"]`)
      if (option && track.duration && !option.textContent.includes('(')) {
        option.textContent = `${track.name} (${this.formatDuration(track.duration)})`
      }
    } else {
      this.elements.musicTrack.value = ''
    }
  }
  
  /**
   * Show music playback indicator
   */
  showPlaybackIndicator(isPlaying) {
    // Add visual indicator to current track option
    if (!this.elements.musicTrack) return
    
    const selectedOption = this.elements.musicTrack.selectedOptions[0]
    if (selectedOption) {
      if (isPlaying) {
        if (!selectedOption.textContent.includes('♪')) {
          selectedOption.textContent = '♪ ' + selectedOption.textContent
        }
      } else {
        selectedOption.textContent = selectedOption.textContent.replace('♪ ', '')
      }
    }
  }
  
  /**
   * Format duration for display
   */
  formatDuration(seconds) {
    if (!seconds || isNaN(seconds)) return '0:00'
    
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
  
  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  /**
   * Clear file input
   */
  clearFileInput() {
    if (this.elements.musicFileInput) {
      this.elements.musicFileInput.value = ''
    }
  }
  
  /**
   * Show error message
   */
  showError(message) {
    this.showStatus(message, 'error')
  }
  
  /**
   * Show success message
   */
  showSuccess(message) {
    this.showStatus(message, 'success')
  }
  
  /**
   * Get DOM element for external access
   */
  getElement(name) {
    return this.elements[name]
  }
  
  /**
   * Update all music settings
   */
  updateSettings(settings) {
    this.updateMusicEnabled(settings.enabled)
    this.updateVolumeDisplay(settings.volume)
    
    if (settings.tracks) {
      this.updateTracksList(settings.tracks, settings.currentTrack)
    }
  }
}
